#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
from pathlib import Path

def move_folder_contents(source_dir, target_dir):
    """
    将源目录中的每个文件夹内容移动到目标目录中
    如果文件名中含有"hhd800@"字符串，则在移动前移除该字符串
    
    Args:
        source_dir: 源目录路径
        target_dir: 目标目录路径
    """
    # 确保目标目录存在
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
    
    # 获取源目录中的所有子目录
    source_path = Path(source_dir)
    subdirectories = [f for f in source_path.iterdir() if f.is_dir()]
    
    if not subdirectories:
        print(f"警告：在 {source_dir} 中没有找到任何子目录")
        return
    
    # 遍历每个子目录
    for subdir in subdirectories:
        print(f"正在处理子目录：{subdir.name}")
        
        # 获取子目录中的所有文件和文件夹
        for item in subdir.glob('*'):
            # 处理文件名中的"hhd800@"字符串
            new_name = item.name.replace("hhd800@", "")
            
            # 构建目标路径（使用处理后的名称）
            dest_path = Path(target_dir) / new_name
            
            # 如果是文件，直接移动
            if item.is_file():
                # 如果目标文件已存在，先删除
                if dest_path.exists():
                    dest_path.unlink()
                shutil.move(item, dest_path)
                if new_name != item.name:
                    print(f"  已移动并重命名文件：{item.name} -> {new_name}")
                else:
                    print(f"  已移动文件：{item.name}")
            
            # 如果是目录，移动
            elif item.is_dir():
                # 如果目标目录已存在，先删除
                if dest_path.exists():
                    shutil.rmtree(dest_path)
                shutil.move(item, dest_path)
                if new_name != item.name:
                    print(f"  已移动并重命名目录：{item.name} -> {new_name}")
                else:
                    print(f"  已移动目录：{item.name}")

def main():
    source_directory = "/Volumes/ghs/video/temp/云下载"
    target_directory = "/Volumes/ghs/video/temp/temp"
    
    print(f"开始从 {source_directory} 移动到 {target_directory}")
    move_folder_contents(source_directory, target_directory)
    print("移动完成！")

if __name__ == "__main__":
    main()
