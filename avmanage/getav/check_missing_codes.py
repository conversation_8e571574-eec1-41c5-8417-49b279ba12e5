#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import sys
import argparse
from pathlib import Path

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent.parent))
from av_config import *

def read_codes_from_unified_file(file_path, enabled_types=None):
    """从统一番号文件读取指定类型的番号，返回一个集合"""
    if enabled_types is None:
        enabled_types = ENABLED_CODE_TYPES
    
    codes = set()
    current_type = None
    current_type_key = None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            # 跳过空行
            if not line:
                continue
            
            # 检查是否是类型标题行
            if line.startswith('#'):
                type_name = line[1:].strip()
                # 查找对应的类型键
                for key, name in AVAILABLE_CODE_TYPES.items():
                    if name == type_name:
                        current_type = type_name
                        current_type_key = key
                        break
                continue
            
            # 如果当前类型在启用的类型列表中，提取番号
            if current_type_key and current_type_key in enabled_types:
                # 提取番号部分（去除日期等信息）
                # 格式如：CAWD-808 (2025-03-28) [单体作品]
                code_match = re.match(r'^([A-Z0-9-]+)', line)
                if code_match:
                    codes.add(code_match.group(1))
    
    return codes

def read_codes_from_file(file_path):
    """读取番号列表文件，返回一个集合（保留兼容性）"""
    codes = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            # 跳过空行
            if not line:
                continue
            # 跳过分类标题行（以#开头的行）
            if line.startswith('#'):
                continue
            # 提取番号部分（去除日期等信息）
            # 格式如：CAWD-808 (2025-03-28)
            code_match = re.match(r'^([A-Z0-9-]+)', line)
            if code_match:
                codes.add(code_match.group(1))
    return codes

def extract_codes_from_file_list(file_path):
    """从文件列表中提取所有番号，返回一个集合"""
    codes_in_files = set()
    
    # 编译正则表达式以匹配番号
    # 匹配格式如：CAWD-123, KAWD-123等
    code_pattern = re.compile(r'([A-Z]+-\d+)')
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            matches = code_pattern.findall(line)
            for match in matches:
                codes_in_files.add(match)
    
    return codes_in_files

def find_missing_codes(all_codes, existing_codes):
    """找出缺失的番号"""
    return all_codes - existing_codes

def find_extra_codes(all_codes, existing_codes):
    """找出额外的番号（在文件夹中存在但不在番号列表中）"""
    return existing_codes - all_codes

def save_missing_codes_to_file(missing_codes, output_file):
    """将缺失的番号保存到文本文件中"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for code in sorted(missing_codes):
            f.write(f"{code}\n")
    print(f"已将 {len(missing_codes)} 个缺失的番号保存到文件：{output_file}")

def list_all_files():
    """生成统一文件列表，将所有类型的文件列到统一文件中"""
    # 获取文件路径配置
    file_paths = get_file_paths()
    unified_file_list = str(file_paths["unified_file_list"])
    
    all_files = []
    
    # 处理普通目录
    print(f"开始处理普通目录: {NORMAL_DIRECTORY}")
    if not os.path.isdir(NORMAL_DIRECTORY):
        print(f"错误: {NORMAL_DIRECTORY} 不是一个有效的目录")
        return False
    
    # 收集普通目录的文件
    normal_files = []
    for root, dirs, files in os.walk(NORMAL_DIRECTORY):
        for file in files:
            file_path = os.path.join(root, file)
            normal_files.append(file_path)
    
    print(f"普通目录中发现 {len(normal_files)} 个文件")
    all_files.extend(normal_files)
    
    # 如果启用VR，处理VR目录
    vr_files = []
    if INCLUDE_VR:
        print(f"\n开始处理VR目录: {VR_DIRECTORY}")
        if not os.path.isdir(VR_DIRECTORY):
            print(f"错误: {VR_DIRECTORY} 不是一个有效的目录")
            return False
        
        # 收集VR目录的文件
        for root, dirs, files in os.walk(VR_DIRECTORY):
            for file in files:
                file_path = os.path.join(root, file)
                vr_files.append(file_path)
        
        print(f"VR目录中发现 {len(vr_files)} 个文件")
        all_files.extend(vr_files)
    
    # 将所有文件写入统一文件
    with open(unified_file_list, 'w', encoding='utf-8') as f:
        # 写入普通文件（添加标识）
        if normal_files:
            f.write("# 普通文件\n")
            for file_path in normal_files:
                f.write(f"{file_path}\n")
            f.write("\n")
        
        # 写入VR文件（添加标识）
        if vr_files:
            f.write("# VR文件\n")
            for file_path in vr_files:
                f.write(f"{file_path}\n")
            f.write("\n")
    
    print(f"\n所有文件列表已保存到统一文件: {unified_file_list}")
    print(f"总计 {len(all_files)} 个文件 (普通: {len(normal_files)}, VR: {len(vr_files)})")
    print("\n文件列表生成完成")
    return True

def main(args=None):
    print("✨ AV文件管理工具 - 统一文件检查工具")
    print("=" * 50)
    
    # 从配置获取文件路径
    file_paths = get_file_paths()
    
    unified_codes_file = file_paths["unified_codes_file"]
    unified_file_list = file_paths["unified_file_list"]
    unified_missing_codes_file = file_paths["unified_missing_codes_file"]
    
    # 如果只是生成文件列表，直接返回
    if args and args.list_only:
        print("📁 只生成文件列表模式")
        success = list_all_files()
        if success:
            print("\n✅ 文件列表生成完成！")
        else:
            print("\n❌ 文件列表生成失败！")
        return
    
    # 确保统一番号文件存在
    if not os.path.exists(unified_codes_file):
        print(f"错误：统一番号文件 {unified_codes_file} 不存在")
        print("请先运行 get_codes.py 生成统一番号文件")
        return
    
    # 检查是否跳过更新文件列表
    skip_update = args and args.skip_update
    
    if not skip_update:
        print(f"\n🔄 更新统一文件列表...")
        if not list_all_files():
            print("生成文件列表失败，程序退出")
            return
        print("✅ 文件列表生成成功\n")
    else:
        if not os.path.exists(unified_file_list):
            print(f"\n⚠️  统一文件列表 {unified_file_list} 不存在，需要先生成")
            print("请移除 --skip-update 参数重新运行")
            return
        print(f"\n📁 跳过更新，使用现有文件列表: {unified_file_list}")
    
    # 从统一文件中读取配置启用的所有类型番号
    all_codes = read_codes_from_unified_file(unified_codes_file, ENABLED_CODE_TYPES)
    print(f"从统一文件读取到 {len(all_codes)} 个番号（包含启用的类型：{', '.join([AVAILABLE_CODE_TYPES[t] for t in ENABLED_CODE_TYPES])}）")
    
    # 分别读取不同类型的番号用于详细统计
    normal_codes = read_codes_from_unified_file(unified_codes_file, ["normal"])
    vr_codes = read_codes_from_unified_file(unified_codes_file, ["vr"])
    collaborative_codes = read_codes_from_unified_file(unified_codes_file, ["collaborative"])
    collection_codes = read_codes_from_unified_file(unified_codes_file, ["collection"])
    
    print(f"  - 普通作品: {len(normal_codes)} 个")
    print(f"  - VR作品: {len(vr_codes)} 个")
    print(f"  - 共演作品: {len(collaborative_codes)} 个")
    print(f"  - 合集作品: {len(collection_codes)} 个")
    
    # 从统一文件列表中提取番号
    all_existing_codes = extract_codes_from_file_list(unified_file_list)
    print(f"从统一文件列表 {unified_file_list} 中发现 {len(all_existing_codes)} 个番号")
    
    # 找出所有启用类型中缺失的番号
    missing_codes = find_missing_codes(all_codes, all_existing_codes)
    
    # 分别计算各类型的缺失番号用于详细统计
    missing_normal = find_missing_codes(normal_codes, all_existing_codes) if "normal" in ENABLED_CODE_TYPES else set()
    missing_vr = find_missing_codes(vr_codes, all_existing_codes) if "vr" in ENABLED_CODE_TYPES else set()
    missing_collaborative = find_missing_codes(collaborative_codes, all_existing_codes) if "collaborative" in ENABLED_CODE_TYPES else set()
    missing_collection = find_missing_codes(collection_codes, all_existing_codes) if "collection" in ENABLED_CODE_TYPES else set()
    
    if missing_codes:
        print(f"\n发现 {len(missing_codes)} 个缺失的番号")
        print(f"  - 普通作品: {len(missing_normal)} 个")
        print(f"  - VR作品: {len(missing_vr)} 个")
        print(f"  - 共演作品: {len(missing_collaborative)} 个")
        print(f"  - 合集作品: {len(missing_collection)} 个")
        
        # 保存所有缺失的番号到统一文件
        with open(unified_missing_codes_file, 'w', encoding='utf-8') as f:
            if missing_normal:
                f.write("# 普通作品\n")
                for code in sorted(missing_normal):
                    f.write(f"{code}\n")
                f.write("\n")
            
            if missing_vr:
                f.write("# VR作品\n")
                for code in sorted(missing_vr):
                    f.write(f"{code}\n")
                f.write("\n")
            
            if missing_collaborative:
                f.write("# 共演作品\n")
                for code in sorted(missing_collaborative):
                    f.write(f"{code}\n")
                f.write("\n")
            
            if missing_collection:
                f.write("# 合集作品\n")
                for code in sorted(missing_collection):
                    f.write(f"{code}\n")
                f.write("\n")
        
        print(f"已将所有缺失番号保存到统一文件: {unified_missing_codes_file}")
        
        # 打印缺失的番号
        print("\n缺失的番号列表：")
        for code in sorted(missing_codes):
            print(code)
    else:
        print("\n文件夹中包含所有列表中的番号")
        # 创建空的统一文件
        with open(unified_missing_codes_file, 'w', encoding='utf-8') as f:
            pass
    
    # 找出额外的番号
    extra_codes = find_extra_codes(all_codes, all_existing_codes)
    
    if extra_codes:
        print(f"\n文件夹中存在以下 {len(extra_codes)} 个额外的番号（不在番号列表中）：")
        for code in sorted(extra_codes):
            print(code)
    else:
        print("\n文件夹中没有额外的番号")

if __name__ == "__main__":
    # 设置命令行参数
    parser = argparse.ArgumentParser(
        description="AV文件管理工具 - 统一文件检查工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python check_missing_codes.py                    # 更新文件列表并检查缺失番号（默认）
  python check_missing_codes.py --skip-update      # 跳过文件列表更新，直接检查
  python check_missing_codes.py --list-only        # 只生成文件列表
        """
    )
    
    parser.add_argument(
        "--list-only", "-l",
        action="store_true",
        help="只生成统一文件列表，不进行缺失番号检查"
    )
    
    parser.add_argument(
        "--skip-update", "-s",
        action="store_true",
        help="跳过文件列表更新，使用现有的文件列表（默认会更新）"
    )
    
    args = parser.parse_args()
    main(args)
